# 学生成绩回归分析 - 可视化演示脚本
# 展示散点图、残差图、直方图和QQ图的核心功能

# 加载必要的包
suppressMessages({
  library(ggplot2)
  library(dplyr)
  library(tidyr)
})

cat("=== 学生成绩回归分析可视化演示 ===\n\n")

# 读取数据
cat("1. 读取数据...\n")
if(file.exists("1.csv")) {
  data <- read.csv("1.csv", header = TRUE, stringsAsFactors = FALSE)
  cat("   数据维度:", dim(data), "\n")
  cat("   样本数量:", nrow(data), "个学生\n")
  cat("   变量数量:", ncol(data), "个变量\n\n")
} else {
  stop("错误：找不到数据文件 1.csv")
}

# 数据预处理
cat("2. 数据预处理...\n")
target_var <- "MidExamScore"
predictor_vars <- paste0("PreExamScore", 1:50)

# 检查并填充缺失值
missing_before <- sum(is.na(data))
for(col in c(predictor_vars, target_var)) {
  if(col %in% colnames(data)) {
    data[[col]][is.na(data[[col]])] <- mean(data[[col]], na.rm = TRUE)
  }
}
missing_after <- sum(is.na(data))
cat("   缺失值处理: ", missing_before, "→", missing_after, "\n")

# 选择完整数据
complete_data <- data[complete.cases(data[c(predictor_vars, target_var)]), ]
cat("   完整数据:", nrow(complete_data), "行\n\n")

# 3. 散点图分析 - 展示线性关系
cat("3. 生成散点图分析...\n")

# 选择代表性变量
selected_vars <- c("PreExamScore1", "PreExamScore25", "PreExamScore50")
available_vars <- selected_vars[selected_vars %in% colnames(complete_data)]

if(length(available_vars) > 0) {
  # 准备数据
  scatter_data <- complete_data %>%
    select(all_of(c(available_vars, target_var))) %>%
    pivot_longer(cols = all_of(available_vars), 
                names_to = "考试", values_to = "预考成绩")
  
  # 创建散点图
  p1 <- ggplot(scatter_data, aes(x = `预考成绩`, y = MidExamScore)) +
    geom_point(alpha = 0.6, color = "steelblue", size = 2) +
    geom_smooth(method = "lm", se = TRUE, color = "red", size = 1.2) +
    facet_wrap(~`考试`, scales = "free_x") +
    labs(
      title = "预考成绩与中考成绩的线性关系",
      subtitle = "红线显示线性回归拟合，灰色区域为置信区间",
      x = "预考成绩",
      y = "中考成绩"
    ) +
    theme_minimal() +
    theme(
      plot.title = element_text(hjust = 0.5, size = 16, face = "bold"),
      plot.subtitle = element_text(hjust = 0.5, size = 12),
      strip.text = element_text(size = 12, face = "bold")
    )
  
  print(p1)
  
  # 计算相关系数
  cat("   相关系数分析:\n")
  for(var in available_vars) {
    cor_val <- cor(complete_data[[var]], complete_data[[target_var]])
    cat("   ", var, ":", sprintf("%.3f", cor_val), "\n")
  }
  cat("\n")
}

# 4. 构建回归模型进行残差分析
cat("4. 构建回归模型...\n")

# 使用前5个变量避免过拟合
model_vars <- paste0("PreExamScore", 1:5)
available_model_vars <- model_vars[model_vars %in% colnames(complete_data)]

if(length(available_model_vars) >= 2) {
  # 构建模型
  formula_str <- paste(target_var, "~", paste(available_model_vars, collapse = " + "))
  model <- lm(as.formula(formula_str), data = complete_data)
  
  # 模型信息
  r_squared <- summary(model)$r.squared
  cat("   使用变量:", length(available_model_vars), "个\n")
  cat("   R-squared:", sprintf("%.4f", r_squared), "\n")
  cat("   模型解释了", sprintf("%.1f%%", r_squared * 100), "的方差\n\n")
  
  # 获取残差和拟合值
  fitted_vals <- fitted(model)
  residuals_vals <- residuals(model)
  
  # 5. 残差分析图
  cat("5. 生成残差分析图...\n")
  
  residual_df <- data.frame(
    拟合值 = fitted_vals,
    残差 = residuals_vals
  )
  
  # 残差vs拟合值图
  p2 <- ggplot(residual_df, aes(x = `拟合值`, y = `残差`)) +
    geom_point(alpha = 0.6, color = "steelblue", size = 2) +
    geom_hline(yintercept = 0, linetype = "dashed", color = "red", size = 1) +
    geom_smooth(se = FALSE, color = "orange", size = 1) +
    labs(
      title = "残差分析图",
      subtitle = "检查模型拟合质量和同方差性假设",
      x = "拟合值",
      y = "残差"
    ) +
    theme_minimal() +
    theme(
      plot.title = element_text(hjust = 0.5, size = 16, face = "bold"),
      plot.subtitle = element_text(hjust = 0.5, size = 12)
    )
  
  print(p2)
  
  # 6. 正态性检验图
  cat("6. 生成正态性检验图...\n")
  
  # 直方图
  p3 <- ggplot(residual_df, aes(x = `残差`)) +
    geom_histogram(aes(y = after_stat(density)), 
                  bins = 12, fill = "lightblue", 
                  color = "black", alpha = 0.7) +
    geom_density(color = "red", size = 1.5) +
    stat_function(fun = dnorm, 
                 args = list(mean = mean(residuals_vals), 
                            sd = sd(residuals_vals)),
                 color = "blue", size = 1.5, linetype = "dashed") +
    labs(
      title = "残差分布直方图",
      subtitle = "红线：实际分布，蓝虚线：理论正态分布",
      x = "残差",
      y = "密度"
    ) +
    theme_minimal() +
    theme(
      plot.title = element_text(hjust = 0.5, size = 16, face = "bold"),
      plot.subtitle = element_text(hjust = 0.5, size = 12)
    )
  
  print(p3)
  
  # Q-Q图
  p4 <- ggplot(residual_df, aes(sample = `残差`)) +
    stat_qq(color = "steelblue", alpha = 0.7, size = 2) +
    stat_qq_line(color = "red", size = 1.5) +
    labs(
      title = "残差Q-Q图",
      subtitle = "检验正态性假设：点越接近红线，越符合正态分布",
      x = "理论分位数",
      y = "样本分位数"
    ) +
    theme_minimal() +
    theme(
      plot.title = element_text(hjust = 0.5, size = 16, face = "bold"),
      plot.subtitle = element_text(hjust = 0.5, size = 12)
    )
  
  print(p4)
  
  # 7. 统计检验
  cat("7. 统计检验结果...\n")
  
  # 残差统计
  cat("   残差统计信息:\n")
  cat("   - 均值:", sprintf("%.6f", mean(residuals_vals)), "(应接近0)\n")
  cat("   - 标准差:", sprintf("%.3f", sd(residuals_vals)), "\n")
  cat("   - 范围:", sprintf("[%.3f, %.3f]", min(residuals_vals), max(residuals_vals)), "\n")
  
  # 正态性检验
  if(length(residuals_vals) <= 5000) {
    shapiro_test <- shapiro.test(residuals_vals)
    cat("\n   Shapiro-Wilk正态性检验:\n")
    cat("   - W统计量:", sprintf("%.4f", shapiro_test$statistic), "\n")
    cat("   - p值:", sprintf("%.4f", shapiro_test$p.value), "\n")
    cat("   - 结论:", ifelse(shapiro_test$p.value > 0.05, 
                          "残差符合正态分布 ✓", 
                          "残差不符合正态分布 ✗"), "\n")
  }
  
} else {
  cat("   错误：可用变量不足，无法构建模型\n")
}

# 8. 总结
cat("\n=== 可视化分析总结 ===\n")
cat("✓ 散点图：展示了预考成绩与中考成绩的线性关系\n")
cat("✓ 残差图：检查了模型拟合质量和同方差性\n")
cat("✓ 直方图：验证了残差分布的正态性\n")
cat("✓ Q-Q图：进一步确认了正态性假设\n")
cat("✓ 统计检验：提供了定量的模型诊断结果\n\n")

cat("所有图表已保存到 Rplots.pdf 文件中\n")
cat("这些可视化分析验证了线性回归模型的基本假设，\n")
cat("为模型的可靠性提供了图形化证据。\n")

cat("\n演示完成！\n")
