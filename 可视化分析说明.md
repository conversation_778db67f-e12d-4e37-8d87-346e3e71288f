# 学生成绩回归分析 - 可视化分析说明

## 概述

根据您的要求，我已经为学生成绩回归分析添加了详细的可视化分析功能，包括散点图、残差图、直方图和QQ图。这些图表用于验证线性回归模型的基本假设和分析模型的拟合效果。

## 已实现的可视化分析

### 1. 散点图分析 (自变量与因变量的线性关系)

**目的**: 检查各预考成绩与中考成绩之间是否存在线性关系

**实现功能**:
- 选择代表性的预考成绩（PreExamScore1, 10, 25, 40, 50）
- 绘制每个预考成绩与中考成绩的散点图
- 添加线性回归拟合线和置信区间
- 计算并显示相关系数

**分析意义**:
- 如果散点图显示明显的线性趋势，说明线性回归模型是合适的
- 相关系数越接近1或-1，线性关系越强

### 2. 残差分析图

**目的**: 检查回归模型的拟合情况和同方差性假设

**实现功能**:
- **残差 vs 拟合值图**: 检查残差是否随机分布
- **标准化残差图**: 识别异常值（超出±2标准差的点）
- 添加水平参考线和平滑曲线

**分析意义**:
- 残差随机分布且无明显模式 → 模型拟合良好
- 残差呈现漏斗形或其他模式 → 可能存在异方差性问题
- 标准化残差超出±2的点可能是异常值

### 3. 正态性检验图

**目的**: 检验残差是否符合正态分布假设

**实现功能**:
- **残差直方图**: 显示残差的分布形状
  - 实际密度曲线（红线）
  - 理论正态分布曲线（蓝虚线）
- **Q-Q图**: 比较残差分位数与理论正态分位数
  - 如果点接近直线，说明符合正态分布

**分析意义**:
- 直方图接近钟形且与理论正态分布重合 → 正态性假设成立
- Q-Q图中的点接近直线 → 正态性假设成立
- 偏离正态分布可能影响统计推断的有效性

### 4. 模型比较可视化

**目的**: 比较不同回归模型的预测性能

**实现功能**:
- **实际值 vs 预测值散点图**: 展示各模型的预测准确性
- **模型性能指标柱状图**: 比较MAE和RMSE指标
- 添加完美预测参考线（y=x）

**分析意义**:
- 点越接近y=x直线，预测越准确
- MAE和RMSE越小，模型性能越好

## 统计检验

### 正态性检验
- **Shapiro-Wilk检验**: 适用于小样本（n≤5000）
- **Kolmogorov-Smirnov检验**: 适用于大样本
- p值 > 0.05 表示残差符合正态分布

### 残差统计信息
- 残差均值（应接近0）
- 残差标准差
- 残差范围

## 代码结构

### 主要函数

1. `create_scatter_plots()`: 生成散点图分析
2. `create_residual_analysis()`: 生成残差分析图
3. `create_normality_plots()`: 生成正态性检验图
4. `create_model_comparison_plots()`: 生成模型比较图

### 执行流程

```r
# 1. 生成自变量与因变量散点图
create_scatter_plots()

# 2. 生成残差分析图
create_residual_analysis()

# 3. 生成正态性检验图
create_normality_plots()

# 4. 生成模型比较图
create_model_comparison_plots()
```

## 输出文件

- **Rplots.pdf**: 包含所有生成的图表
- **控制台输出**: 详细的统计信息和分析结论

## 分析结论模板

根据可视化结果，分析报告会包含以下结论：

1. **线性关系**: 散点图显示预考成绩与中考成绩存在一定的线性关系
2. **模型拟合**: 残差图显示残差分布相对随机，模型拟合较为合理
3. **正态性**: 残差基本符合正态分布假设
4. **模型比较**: 岭回归在处理高维问题上表现优于OLS

## 使用说明

1. 确保已安装必要的R包：`ggplot2`, `dplyr`, `tidyr`
2. 运行 `student_regression_analysis.R` 脚本
3. 查看生成的 `Rplots.pdf` 文件中的图表
4. 参考控制台输出的统计检验结果

## 技术特点

- **自动化**: 所有图表自动生成，无需手动干预
- **标准化**: 使用一致的图表样式和配色方案
- **全面性**: 涵盖线性回归模型诊断的所有重要方面
- **可解释性**: 每个图表都有清晰的标题和说明

这套可视化分析系统为您的回归分析提供了完整的图形化诊断工具，帮助验证模型假设和评估模型性能。
