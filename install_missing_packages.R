# 安装缺失的包
user_lib <- file.path(Sys.getenv("R_USER"), "library")
.libPaths(c(user_lib, .libPaths()))

# 尝试安装缺失的包
tryCatch({
  install.packages("stringi", repos="https://cloud.r-project.org/", lib=user_lib)
}, error = function(e) {
  cat("stringi安装失败:", e$message, "\n")
})

tryCatch({
  install.packages("vroom", repos="https://cloud.r-project.org/", lib=user_lib)
}, error = function(e) {
  cat("vroom安装失败:", e$message, "\n")
})

cat("缺失包安装尝试完成!\n")
