# 快速生成PNG图片的脚本
# 专门用于生成高质量的可视化图片

cat("=== 学生成绩分析 - PNG图片生成器 ===\n\n")

# 检查并加载必要的包
required_packages <- c("ggplot2", "dplyr", "tidyr")
for(pkg in required_packages) {
  if(!require(pkg, character.only = TRUE, quietly = TRUE)) {
    cat("正在安装包:", pkg, "\n")
    install.packages(pkg, repos = "https://cran.rstudio.com/")
    library(pkg, character.only = TRUE)
  }
}

# 创建plots目录
if(!dir.exists("plots")) {
  dir.create("plots")
  cat("✓ 创建plots目录\n")
}

# 读取数据
cat("1. 读取数据...\n")
if(!file.exists("1.csv")) {
  stop("错误：找不到数据文件 1.csv")
}

data <- read.csv("1.csv", header = TRUE, stringsAsFactors = FALSE)
cat("   数据维度:", dim(data), "\n")

# 数据预处理
cat("2. 数据预处理...\n")
target_var <- "MidExamScore"
predictor_vars <- paste0("PreExamScore", 1:50)

# 填充缺失值
for(col in c(predictor_vars, target_var)) {
  if(col %in% colnames(data)) {
    data[[col]][is.na(data[[col]])] <- mean(data[[col]], na.rm = TRUE)
  }
}

complete_data <- data[complete.cases(data[c(predictor_vars, target_var)]), ]
cat("   完整数据:", nrow(complete_data), "行\n")

# 生成图片1：散点图分析
cat("3. 生成散点图分析...\n")
selected_vars <- c("PreExamScore1", "PreExamScore25", "PreExamScore50")
available_vars <- selected_vars[selected_vars %in% colnames(complete_data)]

if(length(available_vars) > 0) {
  scatter_data <- complete_data %>%
    select(all_of(c(available_vars, target_var))) %>%
    pivot_longer(cols = all_of(available_vars), 
                names_to = "考试类型", values_to = "预考成绩")
  
  p1 <- ggplot(scatter_data, aes(x = `预考成绩`, y = MidExamScore)) +
    geom_point(alpha = 0.6, color = "steelblue", size = 2) +
    geom_smooth(method = "lm", se = TRUE, color = "red", size = 1.2) +
    facet_wrap(~`考试类型`, scales = "free_x") +
    labs(
      title = "预考成绩与中考成绩的线性关系分析",
      subtitle = "红线为线性回归拟合线，灰色区域为95%置信区间",
      x = "预考成绩",
      y = "中考成绩",
      caption = "数据来源：学生成绩数据集"
    ) +
    theme_minimal() +
    theme(
      plot.title = element_text(hjust = 0.5, size = 16, face = "bold"),
      plot.subtitle = element_text(hjust = 0.5, size = 12),
      strip.text = element_text(size = 12, face = "bold"),
      plot.caption = element_text(size = 10, color = "gray50")
    )
  
  ggsave("plots/散点图分析.png", plot = p1, 
         width = 12, height = 8, dpi = 300, bg = "white")
  cat("   ✓ 散点图已保存: plots/散点图分析.png\n")
}

# 生成图片2：回归模型和残差分析
cat("4. 生成残差分析图...\n")
model_vars <- paste0("PreExamScore", 1:5)
available_model_vars <- model_vars[model_vars %in% colnames(complete_data)]

if(length(available_model_vars) >= 2) {
  # 构建简单回归模型
  formula_str <- paste(target_var, "~", paste(available_model_vars, collapse = " + "))
  model <- lm(as.formula(formula_str), data = complete_data)
  
  fitted_vals <- fitted(model)
  residuals_vals <- residuals(model)
  
  residual_df <- data.frame(
    拟合值 = fitted_vals,
    残差 = residuals_vals
  )
  
  # 残差分析图
  p2 <- ggplot(residual_df, aes(x = `拟合值`, y = `残差`)) +
    geom_point(alpha = 0.6, color = "steelblue", size = 2) +
    geom_hline(yintercept = 0, linetype = "dashed", color = "red", size = 1) +
    geom_smooth(se = FALSE, color = "orange", size = 1) +
    labs(
      title = "残差分析图",
      subtitle = "检查模型拟合质量和同方差性假设",
      x = "拟合值",
      y = "残差",
      caption = "红色虚线为零残差线，橙色线为残差趋势"
    ) +
    theme_minimal() +
    theme(
      plot.title = element_text(hjust = 0.5, size = 16, face = "bold"),
      plot.subtitle = element_text(hjust = 0.5, size = 12),
      plot.caption = element_text(size = 10, color = "gray50")
    )
  
  ggsave("plots/残差分析图.png", plot = p2, 
         width = 10, height = 8, dpi = 300, bg = "white")
  cat("   ✓ 残差分析图已保存: plots/残差分析图.png\n")
  
  # 正态性检验图
  p3 <- ggplot(residual_df, aes(x = `残差`)) +
    geom_histogram(aes(y = after_stat(density)), 
                  bins = 15, fill = "lightblue", 
                  color = "black", alpha = 0.7) +
    geom_density(color = "red", size = 1.5) +
    stat_function(fun = dnorm, 
                 args = list(mean = mean(residuals_vals), 
                            sd = sd(residuals_vals)),
                 color = "blue", size = 1.5, linetype = "dashed") +
    labs(
      title = "残差分布直方图",
      subtitle = "检验残差正态性假设",
      x = "残差",
      y = "密度",
      caption = "红线：实际分布，蓝虚线：理论正态分布"
    ) +
    theme_minimal() +
    theme(
      plot.title = element_text(hjust = 0.5, size = 16, face = "bold"),
      plot.subtitle = element_text(hjust = 0.5, size = 12),
      plot.caption = element_text(size = 10, color = "gray50")
    )
  
  ggsave("plots/残差分布直方图.png", plot = p3, 
         width = 10, height = 8, dpi = 300, bg = "white")
  cat("   ✓ 残差直方图已保存: plots/残差分布直方图.png\n")
  
  # Q-Q图
  p4 <- ggplot(residual_df, aes(sample = `残差`)) +
    stat_qq(color = "steelblue", alpha = 0.7, size = 2) +
    stat_qq_line(color = "red", size = 1.5) +
    labs(
      title = "残差Q-Q图",
      subtitle = "正态性检验：点越接近红线，越符合正态分布",
      x = "理论分位数",
      y = "样本分位数",
      caption = "用于检验残差是否符合正态分布假设"
    ) +
    theme_minimal() +
    theme(
      plot.title = element_text(hjust = 0.5, size = 16, face = "bold"),
      plot.subtitle = element_text(hjust = 0.5, size = 12),
      plot.caption = element_text(size = 10, color = "gray50")
    )
  
  ggsave("plots/残差QQ图.png", plot = p4, 
         width = 10, height = 8, dpi = 300, bg = "white")
  cat("   ✓ Q-Q图已保存: plots/残差QQ图.png\n")
}

# 总结
cat("\n=== 图片生成完成 ===\n")
cat("生成的PNG图片文件：\n")
cat("📊 散点图分析.png - 预考成绩与中考成绩的线性关系\n")
cat("📈 残差分析图.png - 残差vs拟合值，检查同方差性\n")
cat("📉 残差分布直方图.png - 残差分布与正态分布对比\n")
cat("📋 残差QQ图.png - 正态性检验Q-Q图\n\n")

cat("图片规格：\n")
cat("- 分辨率：300 DPI（高清打印质量）\n")
cat("- 格式：PNG（支持透明背景）\n")
cat("- 背景：白色（适合文档插入）\n")
cat("- 尺寸：10-12英寸宽，8英寸高\n\n")

cat("🎉 所有图片已生成到plots目录！\n")
cat("这些图片可直接用于报告、论文或演示文稿。\n")
