# 可视化测试脚本
# 测试散点图、残差图、直方图和QQ图的生成

# 加载必要的包
library(ggplot2)
library(dplyr)
library(tidyr)

# 设置中文字体支持
if(.Platform$OS.type == "windows") {
  windowsFonts(myFont = windowsFont("SimSun"))
  theme_set(theme_minimal(base_family = "myFont"))
} else {
  theme_set(theme_minimal())
}

# 读取数据
cat("正在读取数据...\n")
data <- read.csv("1.csv", header = TRUE, stringsAsFactors = FALSE)

# 检查数据结构
cat("数据维度:", dim(data), "\n")
cat("列名:", colnames(data)[1:10], "...\n")

# 数据预处理
target_var <- "MidExamScore"
predictor_vars <- paste0("PreExamScore", 1:50)

# 检查缺失值
missing_count <- sum(is.na(data))
cat("缺失值总数:", missing_count, "\n")

# 填充缺失值（使用均值）
for(col in c(predictor_vars, target_var)) {
  if(col %in% colnames(data)) {
    data[[col]][is.na(data[[col]])] <- mean(data[[col]], na.rm = TRUE)
  }
}

# 选择完整的数据
complete_data <- data[complete.cases(data[c(predictor_vars, target_var)]), ]
cat("完整数据行数:", nrow(complete_data), "\n")

# 1. 创建散点图 - 展示自变量与因变量的线性关系
cat("\n=== 生成散点图 ===\n")

# 选择几个代表性的预考成绩
selected_exams <- c("PreExamScore1", "PreExamScore10", "PreExamScore25", 
                   "PreExamScore40", "PreExamScore50")

# 准备散点图数据
scatter_data <- complete_data %>%
  select(all_of(c(selected_exams, target_var))) %>%
  pivot_longer(cols = all_of(selected_exams), 
              names_to = "Exam", values_to = "Score")

# 创建散点图
p1 <- ggplot(scatter_data, aes(x = Score, y = MidExamScore)) +
  geom_point(alpha = 0.7, color = "steelblue", size = 1.5) +
  geom_smooth(method = "lm", se = TRUE, color = "red", linetype = "solid") +
  facet_wrap(~Exam, scales = "free_x", ncol = 3) +
  labs(
    title = "各预考成绩与中考成绩的线性关系",
    x = "预考成绩",
    y = "中考成绩"
  ) +
  theme_minimal() +
  theme(
    plot.title = element_text(hjust = 0.5, size = 14),
    strip.text = element_text(size = 10)
  )

print(p1)

# 计算并显示相关系数
cat("\n各预考成绩与中考成绩的相关系数:\n")
for (exam in selected_exams) {
  if(exam %in% colnames(complete_data)) {
    cor_val <- cor(complete_data[[exam]], complete_data[[target_var]], 
                   use = "complete.obs")
    cat(sprintf("%s: %.3f\n", exam, cor_val))
  }
}

# 2. 构建简单的线性回归模型用于残差分析
cat("\n=== 构建回归模型 ===\n")

# 使用前10个预考成绩构建模型（避免p>n问题）
model_vars <- paste0("PreExamScore", 1:10)
available_vars <- model_vars[model_vars %in% colnames(complete_data)]

if(length(available_vars) > 0) {
  # 构建回归模型
  formula_str <- paste(target_var, "~", paste(available_vars, collapse = " + "))
  model <- lm(as.formula(formula_str), data = complete_data)
  
  # 获取残差和拟合值
  fitted_values <- fitted(model)
  residuals_values <- residuals(model)
  
  cat("模型构建成功，使用变量:", length(available_vars), "个\n")
  cat("R-squared:", round(summary(model)$r.squared, 4), "\n")
  
  # 3. 创建残差图
  cat("\n=== 生成残差分析图 ===\n")
  
  residual_data <- data.frame(
    Fitted = fitted_values,
    Residuals = residuals_values,
    Standardized_Residuals = scale(residuals_values)[,1]
  )
  
  # 残差 vs 拟合值图
  p2 <- ggplot(residual_data, aes(x = Fitted, y = Residuals)) +
    geom_point(alpha = 0.7, color = "steelblue") +
    geom_hline(yintercept = 0, linetype = "dashed", color = "red", size = 1) +
    geom_smooth(se = FALSE, color = "orange", linetype = "solid") +
    labs(
      title = "残差 vs 拟合值图",
      subtitle = "检查同方差性假设",
      x = "拟合值",
      y = "残差"
    ) +
    theme_minimal() +
    theme(plot.title = element_text(hjust = 0.5))
  
  print(p2)
  
  # 4. 创建正态性检验图
  cat("\n=== 生成正态性检验图 ===\n")
  
  # 残差直方图
  p3 <- ggplot(residual_data, aes(x = Residuals)) +
    geom_histogram(aes(y = after_stat(density)), 
                  bins = 15, fill = "lightblue", 
                  color = "black", alpha = 0.7) +
    geom_density(color = "red", size = 1) +
    stat_function(fun = dnorm, 
                 args = list(mean = mean(residuals_values), 
                            sd = sd(residuals_values)),
                 color = "blue", size = 1, linetype = "dashed") +
    labs(
      title = "残差分布直方图",
      subtitle = "红线：实际密度，蓝虚线：理论正态分布",
      x = "残差",
      y = "密度"
    ) +
    theme_minimal() +
    theme(plot.title = element_text(hjust = 0.5))
  
  print(p3)
  
  # Q-Q图
  p4 <- ggplot(residual_data, aes(sample = Residuals)) +
    stat_qq(color = "steelblue", alpha = 0.7) +
    stat_qq_line(color = "red", size = 1) +
    labs(
      title = "残差Q-Q图",
      subtitle = "检查正态性假设",
      x = "理论分位数",
      y = "样本分位数"
    ) +
    theme_minimal() +
    theme(plot.title = element_text(hjust = 0.5))
  
  print(p4)
  
  # 5. 统计检验
  cat("\n=== 统计检验结果 ===\n")
  
  # 残差统计信息
  cat("残差统计信息:\n")
  cat("残差均值:", round(mean(residuals_values), 6), "\n")
  cat("残差标准差:", round(sd(residuals_values), 3), "\n")
  cat("残差范围:", round(range(residuals_values), 3), "\n")
  
  # 正态性检验
  if(length(residuals_values) <= 5000) {
    shapiro_result <- shapiro.test(residuals_values)
    cat("\nShapiro-Wilk正态性检验:\n")
    cat("W =", round(shapiro_result$statistic, 4), 
        ", p值 =", round(shapiro_result$p.value, 4), "\n")
    if(shapiro_result$p.value > 0.05) {
      cat("结论: 残差符合正态分布 (p > 0.05)\n")
    } else {
      cat("结论: 残差不符合正态分布 (p ≤ 0.05)\n")
    }
  }
  
} else {
  cat("错误：没有找到可用的预测变量\n")
}

cat("\n=== 可视化分析完成 ===\n")
cat("所有图表已生成并保存到 Rplots.pdf\n")
cat("分析结论:\n")
cat("1. 散点图显示了预考成绩与中考成绩之间的线性关系\n")
cat("2. 残差图用于检查模型的拟合情况和同方差性假设\n")
cat("3. 直方图和Q-Q图用于检验残差的正态性\n")
cat("4. 这些图表有助于验证线性回归模型的基本假设\n")
