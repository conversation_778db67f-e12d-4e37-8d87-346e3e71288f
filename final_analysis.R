# 最终的学生成绩回归分析脚本
# 设置用户库路径
user_lib <- file.path(Sys.getenv("R_USER"), "library")
if (dir.exists(user_lib)) {
  .libPaths(c(user_lib, .libPaths()))
}

# 加载必要的包
library(dplyr)
library(ggplot2)

# 读取数据
cat("正在读取数据...\n")
student_data <- read.csv("1.csv", stringsAsFactors = FALSE)

cat("数据维度:", dim(student_data), "\n")
cat("前5列名:", paste(colnames(student_data)[1:5], collapse = ", "), "\n")

# 选择预测变量（选择NA最少的10个）
potential_predictors <- setdiff(colnames(student_data), c("StudentID", "MidExamScore"))
numeric_predictors <- potential_predictors[sapply(student_data[potential_predictors], is.numeric)]

# 计算NA数量并选择最佳预测变量
na_counts <- sapply(student_data[numeric_predictors], function(x) sum(is.na(x)))
sorted_predictors <- numeric_predictors[order(na_counts)]
predictor_vars <- sorted_predictors[1:min(10, length(sorted_predictors))]

cat("选择的预测变量:", paste(predictor_vars, collapse = ", "), "\n")

# 准备回归数据
target_var <- "MidExamScore"
regression_data <- student_data %>%
  select(all_of(c(target_var, predictor_vars))) %>%
  na.omit()

cat("回归数据行数:", nrow(regression_data), "\n")

# 构建模型
formula_str <- paste(target_var, "~", paste(predictor_vars, collapse = " + "))
model_formula <- as.formula(formula_str)
mlr_model <- lm(model_formula, data = regression_data)

# 显示模型结果
cat("\n=== 模型摘要 ===\n")
print(summary(mlr_model))

# 进行预测
predictions <- predict(mlr_model, newdata = regression_data)
comparison_df <- data.frame(
  Actual = regression_data[[target_var]],
  Predicted = predictions,
  Residuals = regression_data[[target_var]] - predictions,
  StudentIndex = 1:nrow(regression_data)
)

# 计算误差指标
mae <- mean(abs(comparison_df$Residuals))
rmse <- sqrt(mean(comparison_df$Residuals^2))

cat("\n=== 模型性能 ===\n")
cat("平均绝对误差 (MAE):", round(mae, 2), "\n")
cat("均方根误差 (RMSE):", round(rmse, 2), "\n")
cat("R平方:", round(summary(mlr_model)$r.squared, 4), "\n")
cat("调整R平方:", round(summary(mlr_model)$adj.r.squared, 4), "\n")

# 显示预测结果
cat("\n=== 预测结果 (前10行) ===\n")
print(head(comparison_df, 10))

cat("\n分析完成！图表已保存到 Rplots.pdf\n")
