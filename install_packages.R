# 安装必要的R包到用户目录
# 创建用户库目录
user_lib <- file.path(Sys.getenv("R_USER"), "library")
if (!dir.exists(user_lib)) {
  dir.create(user_lib, recursive = TRUE)
}

# 设置库路径
.libPaths(c(user_lib, .libPaths()))

# 安装包
packages_to_install <- c("dplyr", "ggplot2", "patchwork", "tidyr",
                        "car", "glmnet", "corrplot")

for (pkg in packages_to_install) {
  if (!requireNamespace(pkg, quietly = TRUE)) {
    cat("安装包:", pkg, "\n")
    install.packages(pkg, repos="https://cloud.r-project.org/", lib=user_lib)
  } else {
    cat("包", pkg, "已安装\n")
  }
}

cat("所有包安装完成!\n")
