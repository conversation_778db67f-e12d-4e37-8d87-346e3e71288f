# 简单的R测试脚本
cat("R环境测试开始...\n")

# 设置用户库路径
user_lib <- file.path(Sys.getenv("R_USER"), "library")
if (dir.exists(user_lib)) {
  .libPaths(c(user_lib, .libPaths()))
  cat("用户库路径已设置:", user_lib, "\n")
}

cat("当前库路径:", paste(.libPaths(), collapse = "; "), "\n")

# 测试基础功能
cat("R版本:", R.version.string, "\n")

# 测试数据读取
if (file.exists("1.csv")) {
  cat("找到数据文件 1.csv\n")
  data <- read.csv("1.csv", stringsAsFactors = FALSE)
  cat("数据维度:", dim(data), "\n")
  cat("列名:", paste(colnames(data)[1:min(5, ncol(data))], collapse = ", "), "...\n")
} else {
  cat("未找到数据文件 1.csv\n")
}

# 测试包加载
packages_to_test <- c("dplyr", "ggplot2", "patchwork")
for (pkg in packages_to_test) {
  if (requireNamespace(pkg, quietly = TRUE)) {
    cat("包", pkg, "可用\n")
  } else {
    cat("包", pkg, "不可用\n")
  }
}

cat("R环境测试完成!\n")
