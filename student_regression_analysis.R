# --------------------------------------------------------------------------
# R 脚本：学生成绩的多元线性回归分析
# --------------------------------------------------------------------------

# 1. 设置库路径并加载必要的库
# --------------------------------------------------------------------------
# 设置用户库路径
user_lib <- file.path(Sys.getenv("R_USER"), "library")
if (dir.exists(user_lib)) {
  .libPaths(c(user_lib, .libPaths()))
}

# 如果尚未安装这些包，请取消注释并运行下一行
# install.packages(c("readr", "dplyr", "ggplot2", "patchwork"))
# 只加载必要的包，readr可能有依赖问题，我们使用基础R读取CSV
library(dplyr)
library(ggplot2)
library(patchwork)

# 2. 加载数据集
# --------------------------------------------------------------------------
# 请确保 '1.csv' 文件位于您的R工作目录中，
# 或者提供文件的完整路径。
file_path <- "1.csv"
# 使用基础R读取CSV文件，尝试不同编码
tryCatch({
  student_data <- read.csv(file_path, stringsAsFactors = FALSE)
}, error = function(e) {
  tryCatch({
    student_data <- read.csv(file_path, stringsAsFactors = FALSE, fileEncoding = "UTF-8")
  }, error = function(e_utf8) {
    tryCatch({
      student_data <- read.csv(file_path, stringsAsFactors = FALSE, fileEncoding = "GBK")
    }, error = function(e_gbk) {
      stop(paste("读取CSV文件时出错: ", e_gbk$message,
                 "\n请确保 '1.csv' 文件在工作目录中，或提供正确的路径，并检查文件编码 (尝试UTF-8或GBK)。"))
    })
  })
})


# 显示数据集的前几行和列名以了解其结构
print("数据集前几行:")
print(head(student_data))
print("列名:")
print(colnames(student_data))
print("数据集摘要:")
print(summary(student_data))

# 3. 准备数据
# --------------------------------------------------------------------------
# 假设前50列是 'Score1', 'Score2', ..., 'Score50'
# 目标变量是 'MidExamScore'.
# 如果您的CSV中的列名不同，请相应调整。

# 检查 'MidExamScore' 列是否存在
if (!"MidExamScore" %in% colnames(student_data)) {
  stop("数据集中未找到 'MidExamScore' 列。请检查列名。")
}

# 选择预测变量 (前50次成绩) 和目标变量
# 我们假设前50列是成绩，如果它们没有明确命名为 Score1, Score2...
# 如果列名是 V1, V2, ..., V50，这将有效。
# 为了更稳健，我们将检查列名。

# 选项 1: 如果列名为 'Score1', 'Score2', ...
score_cols_pattern <- paste0("Score", 1:50)
# 检查这些列中有多少实际存在
actual_score_cols <- intersect(score_cols_pattern, colnames(student_data))

if (length(actual_score_cols) == 50) {
  predictor_vars <- actual_score_cols
} else {
  # 选项 2: 如果没有命名为 'ScoreX'，则假定前50列是预测变量。
  # 这要求 'MidExamScore' 不在前50列中，或者我们需要排除它。
  # 查找非 MidExamScore 或 StudentID 的数值列
  potential_predictors <- setdiff(colnames(student_data), c("StudentID", "MidExamScore"))

  # 在潜在预测变量中筛选数值列
  numeric_predictors <- character(0)
  if (length(potential_predictors) > 0) {
    # 确保只选择数值类型的列
    is_numeric_col <- sapply(student_data[, potential_predictors, drop = FALSE], is.numeric)
    numeric_predictors <- potential_predictors[is_numeric_col]
  }

  # 由于数据中有很多NA值，我们需要选择较少的预测变量
  # 首先计算每列的NA数量，选择NA最少的列
  na_counts <- sapply(student_data[numeric_predictors], function(x) sum(is.na(x)))
  sorted_predictors <- numeric_predictors[order(na_counts)]

  # 选择最多10个预测变量（根据数据质量调整）
  max_predictors <- min(10, length(sorted_predictors))
  predictor_vars <- sorted_predictors[1:max_predictors]

  print(paste("已选择前", max_predictors, "个NA最少的数值型预测变量列:"))
  print(paste(predictor_vars, collapse = ", "))
}

target_var <- "MidExamScore"

# 创建公式字符串
formula_str <- paste(target_var, "~", paste(predictor_vars, collapse = " + "))
model_formula <- as.formula(formula_str)

print(paste("用于回归的公式:", formula_str))

# 选择模型数据
regression_data <- student_data %>%
  select(all_of(c(target_var, predictor_vars))) %>%
  na.omit() # 删除在选定列中包含任何NA值的行

if (nrow(regression_data) < length(predictor_vars) + 2) {
  stop(paste("移除NA后数据点不足 (", nrow(regression_data), ") 无法拟合包含", length(predictor_vars), "个预测变量的模型。至少需要", length(predictor_vars) + 2, "个数据点。"))
}
if (nrow(regression_data) < nrow(student_data)) { # 检查是否所有学生数据都被使用
  warning(paste("由于NA值，部分行已被移除。原始行数:", nrow(student_data), "用于回归的行数:", nrow(regression_data), ". 这可能意味着并非所有30名学生的数据都被用于建模。"))
}


# 4. 构建多元线性回归模型
# --------------------------------------------------------------------------
mlr_model <- lm(model_formula, data = regression_data)

# 5. 分析模型
# --------------------------------------------------------------------------
print("模型摘要:")
print(summary(mlr_model))

# 6. 进行预测
# --------------------------------------------------------------------------
predictions <- predict(mlr_model, newdata = regression_data)

# 7. 比较预测与实际分数
# --------------------------------------------------------------------------
comparison_df <- data.frame(
  Actual = regression_data[[target_var]],
  Predicted = predictions,
  Residuals = regression_data[[target_var]] - predictions
)

# 如果原始数据中有StudentID，并且行数匹配，则加入StudentID
# 这假设 regression_data 中的行顺序与 student_data 中移除了NA后的行顺序一致
# 为了更安全地合并，最好在 regression_data 创建时保留 StudentID (如果存在且需要)
# 或者，如果 student_data 有唯一的行标识符，可以在 na.omit 之前创建它
if ("StudentID" %in% colnames(student_data)) {
  # 创建一个包含原始索引的列，以便在na.omit之后能够匹配
  student_data_indexed <- student_data %>% mutate(original_row_index = row_number())
  regression_data_indexed <- student_data_indexed %>%
    select(original_row_index, all_of(c(target_var, predictor_vars))) %>%
    na.omit()

  # 将StudentID添加到comparison_df
  # 这假设StudentID在student_data_indexed中，并且可以基于original_row_index进行匹配
  ids_for_comparison <- student_data_indexed %>%
    filter(original_row_index %in% regression_data_indexed$original_row_index) %>%
    select(StudentID)

  if(nrow(ids_for_comparison) == nrow(comparison_df)){
    comparison_df$StudentID <- ids_for_comparison$StudentID
  } else {
    # 如果行数不匹配，可能是因为StudentID中有NA或复杂情况，创建一个通用索引
    comparison_df$StudentIndex <- 1:nrow(comparison_df)
    warning("无法将StudentID完美匹配到回归数据中，将使用通用学生索引进行绘图。")
  }
} else {
  comparison_df$StudentIndex <- 1:nrow(comparison_df) # 如果没有StudentID，创建索引
}


print("实际分数与预测分数的比较 (前几行):")
print(head(comparison_df))

# 计算平均绝对误差 (MAE) 和均方根误差 (RMSE)
mae <- mean(abs(comparison_df$Residuals))
rmse <- sqrt(mean(comparison_df$Residuals^2))
print(paste("平均绝对误差 (MAE):", round(mae, 2)))
print(paste("均方根误差 (RMSE):", round(rmse, 2)))

# 8. 数据可视化
# --------------------------------------------------------------------------

# 图1: 实际分数 vs. 预测分数
plot1 <- ggplot(comparison_df, aes(x = Actual, y = Predicted)) +
  geom_point(alpha = 0.7, color = "blue") +
  geom_abline(intercept = 0, slope = 1, linetype = "dashed", color = "red") +
  labs(
    title = "实际中考分数 vs. 预测中考分数",
    x = "实际中考分数",
    y = "预测中考分数"
  ) +
  theme_minimal(base_family = "sans") + # 使用通用字体以避免中文显示问题
  coord_fixed(ratio = 1, xlim = range(c(comparison_df$Actual, comparison_df$Predicted)), ylim = range(c(comparison_df$Actual, comparison_df$Predicted)))


# 图2: 残差 vs. 预测值
plot2 <- ggplot(comparison_df, aes(x = Predicted, y = Residuals)) +
  geom_point(alpha = 0.7, color = "green") +
  geom_hline(yintercept = 0, linetype = "dashed", color = "red") +
  labs(
    title = "残差 vs. 预测值",
    x = "预测中考分数",
    y = "残差"
  ) +
  theme_minimal(base_family = "sans")

# 图3: 残差分布图
plot3 <- ggplot(comparison_df, aes(x = Residuals)) +
  geom_histogram(binwidth = 5, fill = "orange", color = "black", alpha = 0.7) +
  labs(
    title = "残差分布图",
    x = "残差",
    y = "频数"
  ) +
  theme_minimal(base_family = "sans")

# 图4: 部分学生实际分数与预测分数的对比条形图 (例如，前10名)
# 使用 StudentID 或 StudentIndex 作为标识符
comparison_subset <- head(comparison_df, 10)

# 转换数据为长格式以便于绘图
comparison_long <- comparison_subset %>%
  select(-Residuals) %>%
  # 决定使用 StudentID 还是 StudentIndex
  mutate(Identifier = if("StudentID" %in% colnames(.)) as.character(StudentID) else as.character(StudentIndex)) %>%
  select(-any_of(c("StudentID", "StudentIndex"))) %>% # 移除旧的ID列，只保留Identifier
  tidyr::pivot_longer(cols = c("Actual", "Predicted"), names_to = "Type", values_to = "Score")


plot4 <- ggplot(comparison_long, aes(x = reorder(Identifier, Score), y = Score, fill = Type)) +
  geom_bar(stat = "identity", position = position_dodge(width = 0.8), width=0.7) +
  labs(
    title = "部分学生实际分数与预测分数对比",
    x = "学生标识",
    y = "中考分数",
    fill = "分数类型"
  ) +
  theme_minimal(base_family = "sans") +
  theme(axis.text.x = element_text(angle = 45, hjust = 1))


# 显示图表
# 如果需要，使用 patchwork 组合图表
if (nrow(regression_data) > 0) {
  print(plot1)
  print(plot2)
  print(plot3)
  if (nrow(comparison_subset) > 0) print(plot4)

  # 您也可以将它们排列在一个网格中
  # combined_plot <- (plot1 | plot2) / (plot3 | plot4)
  # print(combined_plot + plot_layout(guides = 'collect') + plot_annotation(title = '回归分析总览', theme = theme(plot.title = element_text(family="sans"))))
} else {
  print("由于移除NA后没有数据用于回归，因此跳过绘图。")
}

# 脚本结束
# --------------------------------------------------------------------------
