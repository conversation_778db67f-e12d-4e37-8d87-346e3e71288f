# 学生中考数学成绩预测模型复现报告

## 📊 项目概述

本项目根据论文要求，完整复现了基于50次历史考试成绩预测学生中考数学成绩的机器学习模型，包括数据预处理、多元线性回归、多重共线性诊断、岭回归等完整流程。

## 📈 数据概况

- **数据集**: 30名学生 × 51个变量（50次预考成绩 + 1次中考成绩）
- **有效样本**: 26名学生（4名学生中考成绩缺失）
- **训练集**: 20名学生（80%）
- **测试集**: 6名学生（20%）
- **特征维度**: 50个预考成绩（标准化后）

## 🔧 数据预处理

### 1. 缺失值处理
- **策略**: 使用对应考试在所有学生中的平均值填充
- **缺失情况**: 多个预考成绩存在1-4个缺失值
- **目标变量**: 4名学生的中考成绩缺失

### 2. 数据标准化
- **方法**: Z-score标准化 (均值=0, 标准差=1)
- **应用范围**: 所有50个预测变量

### 3. 数据集划分
- **训练集**: 前80%数据（20名学生）
- **测试集**: 后20%数据（6名学生）

## 🔍 模型建立与诊断

### 1. 多元线性回归 (OLS)
- **问题**: p (50) > n (20)，高维问题导致模型不稳定
- **结果**: 完美拟合训练数据（R² = 1），但存在过拟合
- **残差**: 训练集残差为0，无残差自由度

### 2. 多重共线性诊断
- **相关性分析**: 未发现高相关性变量对 (|r| > 0.8)
- **VIF计算**: 由于模型奇异性无法计算
- **结论**: 高维问题导致的数值不稳定

### 3. 岭回归 (Ridge Regression)
- **交叉验证最佳λ**: 906.48
- **论文参考λ**: 0.5
- **优势**: 有效处理多重共线性和高维问题

## 📊 模型性能比较

### 测试集性能指标

| 模型 | MAE | RMSE | R² |
|------|-----|------|----|
| OLS | 32.435 | 55.532 | -1.9113 |
| Ridge (最佳λ) | **21.518** | **32.570** | -0.0015 |
| Ridge (λ=0.5) | 23.726 | 31.727 | 0.0497 |

### 关键发现

1. **最佳模型**: 岭回归（最佳λ）表现最优
2. **OLS问题**: 严重过拟合，测试集性能极差
3. **岭回归优势**: 有效缓解过拟合，提高泛化能力

## 🎯 残差分析

### OLS模型残差检验
- **Shapiro-Wilk正态性检验**: W = 0.921, p = 0.105（接受正态性）
- **Durbin-Watson自相关检验**: 由于模型问题无法计算

## 📈 重要系数分析

### 岭回归（λ=0.5）主要系数
- **PreExamScore45**: 5.194（最重要正向预测因子）
- **PreExamScore17**: -4.505（重要负向预测因子）
- **PreExamScore37**: 2.211（重要正向预测因子）
- **PreExamScore27**: -1.858（负向预测因子）

## 🔬 技术实现

### 使用的R包
- `dplyr`: 数据处理
- `ggplot2`: 数据可视化
- `car`: VIF计算和残差分析
- `glmnet`: 岭回归实现
- `corrplot`: 相关性可视化

### 核心算法
1. **缺失值填充**: 列均值填充
2. **标准化**: Z-score标准化
3. **岭回归**: L2正则化，交叉验证选参
4. **模型评估**: MAE, RMSE, R²

## 💡 结论与建议

### 主要结论
1. **高维问题**: p > n导致OLS不稳定，岭回归是更好选择
2. **预测能力**: 岭回归在测试集上MAE约21.5分，具有实用价值
3. **特征重要性**: 不同时期的考试成绩对最终成绩影响差异显著

### 改进建议
1. **增加样本量**: 收集更多学生数据以改善n < p问题
2. **特征选择**: 使用Lasso回归或其他方法进行特征筛选
3. **模型集成**: 尝试随机森林、支持向量机等其他算法
4. **交叉验证**: 使用k折交叉验证获得更稳健的性能估计

## 📁 项目文件

- `student_regression_analysis.R`: 完整分析脚本
- `install_packages.R`: 包安装脚本
- `1.csv`: 原始数据文件
- `readme.md`: 项目要求说明
- `分析报告.md`: 本报告
- `Rplots.pdf`: 生成的图表

---
**分析完成时间**: 2025-05-27 18:31:31
**使用工具**: R 4.5.0, glmnet, car, ggplot2, dplyr
**项目状态**: ✅ 完成
