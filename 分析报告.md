# 学生成绩多元线性回归分析报告

## 📊 分析概述

本分析使用多元线性回归模型来预测学生的中考成绩，基于他们在50次预考中的表现。

## 📈 数据概况

- **总学生数**: 30名
- **预考次数**: 50次
- **目标变量**: 中考成绩 (MidExamScore)
- **有效数据**: 26名学生（4名学生因中考成绩缺失被排除）

## 🔍 模型构建

### 预测变量选择
由于数据中存在较多缺失值，我们选择了NA值最少的10个预考成绩作为预测变量：
- PreExamScore1, PreExamScore3, PreExamScore6, PreExamScore30
- PreExamScore42, PreExamScore43, PreExamScore46, PreExamScore48
- PreExamScore49, PreExamScore50

### 模型公式
```
MidExamScore ~ PreExamScore1 + PreExamScore3 + PreExamScore6 + PreExamScore30 + 
               PreExamScore42 + PreExamScore43 + PreExamScore46 + PreExamScore48 + 
               PreExamScore49 + PreExamScore50
```

## 📊 模型结果

### 模型性能指标
- **R²**: 0.5351 (53.51%的方差被解释)
- **调整R²**: 0.2252 (22.52%，考虑了变量数量的调整)
- **F统计量**: 1.727 (p值 = 0.1642)
- **残差标准误**: 16.52

### 预测精度
- **平均绝对误差 (MAE)**: 9.1分
- **均方根误差 (RMSE)**: 12.55分

### 显著性预测变量
- **PreExamScore46**: 系数 = 0.484 (p < 0.05, 显著)
- **PreExamScore30**: 系数 = 0.315 (p = 0.098, 边际显著)
- **PreExamScore49**: 系数 = -0.551 (p = 0.072, 边际显著)

## 🎯 主要发现

1. **模型解释力**: 模型能够解释中考成绩53.51%的变异，表明预考成绩对中考成绩有一定的预测能力。

2. **预测精度**: 平均预测误差约为9.1分，在教育评估中属于可接受范围。

3. **关键预测因子**: 
   - PreExamScore46 (第46次预考) 是最重要的正向预测因子
   - PreExamScore49 (第49次预考) 显示负向关系，可能反映考前焦虑或疲劳效应

4. **模型局限性**: 
   - 调整R²较低(22.52%)，表明模型可能存在过拟合
   - F检验不显著(p=0.1642)，整体模型的统计显著性有限

## 💡 建议

1. **数据质量改进**: 减少缺失值，收集更完整的数据
2. **变量筛选**: 考虑使用特征选择方法减少变量数量
3. **模型优化**: 尝试其他回归方法或机器学习算法
4. **样本扩大**: 增加样本量以提高模型的稳定性和泛化能力

## 📁 输出文件

- `student_regression_analysis.R`: 完整分析脚本
- `final_analysis.R`: 简化分析脚本
- `Rplots.pdf`: 回归分析图表
- `分析报告.md`: 本报告

---
*分析完成时间: 2024年*
*使用工具: R 4.5.0, dplyr, ggplot2*
