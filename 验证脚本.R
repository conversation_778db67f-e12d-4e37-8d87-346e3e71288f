# 验证脚本 - 快速测试主要功能
# 设置用户库路径
user_lib <- file.path(Sys.getenv("R_USER"), "library")
if (dir.exists(user_lib)) {
  .libPaths(c(user_lib, .libPaths()))
}

cat("=== 学生中考成绩预测模型验证 ===\n")
cat("验证时间:", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "\n\n")

# 1. 验证包加载
cat("1. 验证R包加载...\n")
required_packages <- c("dplyr", "ggplot2", "car", "glmnet")
for (pkg in required_packages) {
  if (requireNamespace(pkg, quietly = TRUE)) {
    cat("✓", pkg, "包可用\n")
  } else {
    cat("✗", pkg, "包不可用\n")
  }
}

# 2. 验证数据读取
cat("\n2. 验证数据读取...\n")
if (file.exists("1.csv")) {
  data <- read.csv("1.csv", stringsAsFactors = FALSE)
  cat("✓ 数据文件读取成功\n")
  cat("  - 数据维度:", dim(data), "\n")
  cat("  - 列数验证:", ncol(data) == 51, "\n")
  cat("  - 行数验证:", nrow(data) == 30, "\n")
} else {
  cat("✗ 数据文件不存在\n")
}

# 3. 验证核心功能
cat("\n3. 验证核心功能...\n")

# 简单的岭回归测试
library(glmnet)
library(dplyr)

# 准备测试数据
predictor_vars <- paste0("PreExamScore", 1:50)
target_var <- "MidExamScore"

# 缺失值填充
for (col in predictor_vars) {
  if (sum(is.na(data[[col]])) > 0) {
    col_mean <- mean(data[[col]], na.rm = TRUE)
    data[[col]][is.na(data[[col]])] <- col_mean
  }
}

# 标准化
for (col in predictor_vars) {
  col_mean <- mean(data[[col]], na.rm = TRUE)
  col_sd <- sd(data[[col]], na.rm = TRUE)
  data[[paste0(col, "_std")]] <- (data[[col]] - col_mean) / col_sd
}

# 准备建模数据
complete_data <- data[!is.na(data[[target_var]]), ]
predictor_vars_std <- paste0(predictor_vars, "_std")

if (nrow(complete_data) >= 10) {
  X <- as.matrix(complete_data[, predictor_vars_std])
  y <- complete_data[[target_var]]
  
  # 测试岭回归
  tryCatch({
    ridge_model <- glmnet(X, y, alpha = 0, lambda = 0.5)
    predictions <- predict(ridge_model, newx = X)
    mae <- mean(abs(y - predictions))
    
    cat("✓ 岭回归模型构建成功\n")
    cat("  - 训练MAE:", round(mae, 2), "\n")
    cat("  - 模型系数数量:", length(coef(ridge_model)), "\n")
  }, error = function(e) {
    cat("✗ 岭回归模型构建失败:", e$message, "\n")
  })
} else {
  cat("✗ 有效数据不足\n")
}

# 4. 验证输出文件
cat("\n4. 验证输出文件...\n")
output_files <- c("student_regression_analysis.R", "分析报告.md", "Rplots.pdf")
for (file in output_files) {
  if (file.exists(file)) {
    cat("✓", file, "存在\n")
  } else {
    cat("✗", file, "不存在\n")
  }
}

cat("\n=== 验证完成 ===\n")
cat("如果所有项目都显示 ✓，说明环境配置正确，可以运行完整分析\n")
cat("运行完整分析请执行: source('student_regression_analysis.R')\n")
